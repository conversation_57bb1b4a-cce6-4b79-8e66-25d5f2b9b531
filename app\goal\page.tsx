'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { useCalculator } from '@/contexts/CalculatorContext';
import { formatWeight } from '@/lib/units';

export default function GoalPage() {
  const { userData, calculations, proteinPerKg, setProteinPerKg } = useCalculator();
  const [selectedGoal, setSelectedGoal] = useState('stay-fit');
  const [targetWeight, setTargetWeight] = useState(userData.weight.toString());
  const [isTargetWeightEnabled, setIsTargetWeightEnabled] = useState(true);

  // Calculations should always be available when navigating here
  if (!calculations) {
    return null; // This should never be visible with proper navigation
  }

  // Calculate goal-specific calorie target with target weight consideration
  const getCalorieTarget = () => {
    const currentWeight = userData.weight;
    const target = parseFloat(targetWeight);
    const weightDifference = isTargetWeightEnabled ? target - currentWeight : 0;

    // Base calorie targets from calculations
    let baseCalories;
    switch (selectedGoal) {
      case 'lose-weight':
        baseCalories = calculations.calorieTargets.slowLoss;
        // Additional deficit if target weight is significantly lower
        if (weightDifference < -5) baseCalories -= 200;
        break;
      case 'gain-muscles':
        baseCalories = calculations.calorieTargets.muscleGain;
        // Additional surplus if target weight is higher
        if (weightDifference > 5) baseCalories += 200;
        break;
      case 'stay-fit':
        baseCalories = calculations.calorieTargets.maintenance;
        // Slight adjustment based on target weight
        if (Math.abs(weightDifference) > 2) {
          baseCalories += weightDifference > 0 ? 150 : -150;
        }
        break;
      default:
        baseCalories = calculations.calorieTargets.maintenance;
    }

    return Math.max(1200, baseCalories); // Minimum safe calorie intake
  };

  const calorieTarget = getCalorieTarget();

  // Calculate goal-specific macro distribution
  const getMacroDistribution = () => {
    switch (selectedGoal) {
      case 'lose-weight':
        return { protein: 30, carbs: 35, fats: 30, fiber: 5 }; // High protein for satiety
      case 'gain-muscles':
        return { protein: 25, carbs: 45, fats: 25, fiber: 5 }; // High carbs for energy
      case 'stay-fit':
        return { protein: 20, carbs: 50, fats: 25, fiber: 5 }; // Balanced distribution
      default:
        return { protein: 20, carbs: 50, fats: 25, fiber: 5 };
    }
  };

  const macroDistribution = getMacroDistribution();

  // Calculate actual protein intake based on goal and user preference
  const targetWeightForProtein = isTargetWeightEnabled ? parseFloat(targetWeight) : userData.weight;
  const goalProteinIntake = targetWeightForProtein * proteinPerKg;

  // Calculate protein percentage from actual protein needs
  const proteinCalories = goalProteinIntake * 4;
  const calculatedProteinPercentage = (proteinCalories / calorieTarget) * 100;

  // Ensure protein percentage is reasonable (10-40%)
  const proteinPercentage = Math.min(40, Math.max(10, calculatedProteinPercentage));

  // Calculate remaining percentages proportionally
  const remainingPercentage = 100 - proteinPercentage;
  const baseFatPercentage = macroDistribution.fats;
  const baseFiberPercentage = macroDistribution.fiber;
  const baseCarbPercentage = macroDistribution.carbs;

  // Scale non-protein macros proportionally to fit remaining percentage
  const totalNonProtein = baseFatPercentage + baseFiberPercentage + baseCarbPercentage;
  const fatPercentage = (baseFatPercentage / totalNonProtein) * remainingPercentage;
  const fiberPercentage = (baseFiberPercentage / totalNonProtein) * remainingPercentage;
  const carbPercentage = (baseCarbPercentage / totalNonProtein) * remainingPercentage;

  const nutritionData = {
    totalCalories: Math.round(calorieTarget),
    totalKJ: Math.round(calorieTarget * 4.184),
    macros: [
      {
        name: 'Proteins',
        amount: Math.round(goalProteinIntake),
        unit: 'g',
        percentage: Math.round(proteinPercentage),
        color: '#DC2626'
      },
      {
        name: 'Carbohydrate',
        amount: Math.round((calorieTarget * carbPercentage / 100) / 4),
        unit: 'g',
        percentage: Math.round(carbPercentage),
        color: '#2563EB'
      },
      {
        name: 'Fats',
        amount: Math.round((calorieTarget * fatPercentage / 100) / 9),
        unit: 'g',
        percentage: Math.round(fatPercentage),
        color: '#F59E0B'
      },
      {
        name: 'Fiber',
        amount: Math.round((calorieTarget * fiberPercentage / 100) / 4),
        unit: 'g',
        percentage: Math.round(fiberPercentage),
        color: '#16A34A'
      }
    ]
  };

  const goals = [
    { id: 'lose-weight', label: 'Lose weight', active: selectedGoal === 'lose-weight' },
    { id: 'gain-muscles', label: 'Gain muscles', active: selectedGoal === 'gain-muscles' },
    { id: 'stay-fit', label: 'Stay fit', active: selectedGoal === 'stay-fit' }
  ];

  return (
    <div className="h-screen bg-gray-50 flex items-center justify-center overflow-hidden md:overflow-hidden">
      <div className="max-w-2xl mx-auto w-full h-full flex items-center justify-center md:p-4">
        <Card className="shadow-lg w-full h-full flex flex-col md:h-auto md:max-h-full" style={{ backgroundColor: '#F5F5F5' }}>
          <CardHeader className="text-center pb-6 flex-shrink-0">
            <CardTitle className="text-2xl font-bold text-gray-800">
              Your goal
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col overflow-y-auto md:overflow-y-visible">
            <div className="space-y-6 flex-1">

              {/* Goal Selection */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-semibold text-gray-800">I want</span>
                </div>

                <div className="flex space-x-2">
                  {goals.map((goal) => (
                    <button
                      key={goal.id}
                      onClick={() => setSelectedGoal(goal.id)}
                      className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${goal.active
                        ? 'text-white shadow-md'
                        : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                        }`}
                      style={{
                        backgroundColor: goal.active ? '#31860A' : undefined,
                        borderRadius: '12px'
                      }}
                    >
                      {goal.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Target Weight */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-800">Target weight</span>
                  <div className="flex items-center space-x-3">
                    <input
                      type="text"
                      value={`${targetWeight} kg`}
                      onChange={(e) => {
                        const value = e.target.value.replace(' kg', '').replace('kg', '');
                        setTargetWeight(value);
                      }}
                      className="text-center w-24 h-8 font-bold pr-1"
                      style={{
                        backgroundColor: '#F5F5F5',
                        border: 'solid 1px #CFCFCF',
                        borderRadius: '12px',
                        fontSize: '14px'
                      }}
                    />
                    <Switch
                      checked={isTargetWeightEnabled}
                      onCheckedChange={setIsTargetWeightEnabled}
                      style={{
                        backgroundColor: isTargetWeightEnabled ? '#31860A' : '#D1D5DB'
                      }}
                    />
                  </div>
                </div>

                {/* Target Weight Slider */}
                <div className="relative">
                  <div className="w-full h-2 bg-gray-300 rounded-full">
                    {/* Filled track */}
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${Math.min(Math.max(((parseFloat(targetWeight) - (userData.weight - 20)) / 40) * 100, 0), 100)}%`,
                        backgroundColor: '#31860A'
                      }}
                    />
                    <div
                      className="absolute w-6 h-6 bg-white rounded-full shadow-lg cursor-pointer"
                      style={{
                        left: `${Math.min(Math.max(((parseFloat(targetWeight) - (userData.weight - 20)) / 40) * 100, 0), 100)}%`,
                        top: '-8px',
                        transform: 'translateX(-50%)',
                        border: '2px solid #31860A'
                      }}
                    />
                  </div>
                  <input
                    type="range"
                    min={userData.weight - 20}
                    max={userData.weight + 20}
                    value={targetWeight}
                    onChange={(e) => setTargetWeight(e.target.value)}
                    className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer"
                    step="0.5"
                  />
                </div>
              </div>

              {/* Protein Intake Customization */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">
                    Protein intake (g per kg body weight)
                  </span>
                  <Input
                    type="number"
                    value={proteinPerKg}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      if (!isNaN(value) && value >= 0.5 && value <= 3.0) {
                        setProteinPerKg(value);
                      }
                    }}
                    className="text-center w-20 h-8 font-bold"
                    style={{
                      backgroundColor: '#F5F5F5',
                      border: 'solid 1px #CFCFCF',
                      borderRadius: '12px',
                      fontSize: '14px'
                    }}
                    step="0.1"
                    min="0.5"
                    max="3.0"
                  />
                </div>
                <p className="text-xs text-gray-500">
                  Recommended: 0.8-1.2g for general health, 1.6-2.2g for muscle building
                </p>
              </div>

              {/* Recommended Daily Calorie Intake */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">
                  Recommended daily calorie intake
                </h3>

                <div className="flex items-center space-x-6">
                  {/* Animated Pie Chart */}
                  <div className="relative w-24 h-24">
                    <svg width="96" height="96" viewBox="0 0 96 96" className="transform -rotate-90">
                      {/* Background circle */}
                      <circle cx="48" cy="48" r="40" fill="none" stroke="#E5E7EB" strokeWidth="8" />

                      {/* Proteins - Red */}
                      <circle
                        cx="48" cy="48" r="40"
                        fill="none"
                        stroke="#DC2626"
                        strokeWidth="8"
                        strokeDasharray={`${proteinPercentage * 2.51} 251`}
                        strokeDashoffset="0"
                        style={{
                          transition: 'stroke-dasharray 0.5s ease-in-out'
                        }}
                      />

                      {/* Carbs - Blue */}
                      <circle
                        cx="48" cy="48" r="40"
                        fill="none"
                        stroke="#2563EB"
                        strokeWidth="8"
                        strokeDasharray={`${carbPercentage * 2.51} 251`}
                        strokeDashoffset={`-${proteinPercentage * 2.51}`}
                        style={{
                          transition: 'stroke-dasharray 0.5s ease-in-out, stroke-dashoffset 0.5s ease-in-out'
                        }}
                      />

                      {/* Fats - Yellow */}
                      <circle
                        cx="48" cy="48" r="40"
                        fill="none"
                        stroke="#F59E0B"
                        strokeWidth="8"
                        strokeDasharray={`${fatPercentage * 2.51} 251`}
                        strokeDashoffset={`-${(proteinPercentage + carbPercentage) * 2.51}`}
                        style={{
                          transition: 'stroke-dasharray 0.5s ease-in-out, stroke-dashoffset 0.5s ease-in-out'
                        }}
                      />

                      {/* Fiber - Green */}
                      <circle
                        cx="48" cy="48" r="40"
                        fill="none"
                        stroke="#16A34A"
                        strokeWidth="8"
                        strokeDasharray={`${fiberPercentage * 2.51} 251`}
                        strokeDashoffset={`-${(proteinPercentage + carbPercentage + fatPercentage) * 2.51}`}
                        style={{
                          transition: 'stroke-dasharray 0.5s ease-in-out, stroke-dashoffset 0.5s ease-in-out'
                        }}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-12 bg-white rounded-full"></div>
                    </div>
                  </div>

                  {/* Calorie Info */}
                  <div>
                    <div className="text-3xl font-bold text-gray-800">
                      {nutritionData.totalCalories} KCAL
                    </div>
                    <div className="text-lg text-gray-600">
                      = {nutritionData.totalKJ} KJ
                    </div>
                  </div>
                </div>

                {/* Nutrition Breakdown */}
                <div className="space-y-2">
                  {nutritionData.macros.map((macro, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: macro.color }}
                        />
                        <span className="text-sm font-medium" style={{ color: macro.color }}>
                          {macro.name}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm font-medium text-gray-800">
                          {macro.amount} {macro.unit}
                        </span>
                        <span className="text-sm font-medium text-gray-600 w-8 text-right">
                          {macro.percentage}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Navigation Buttons - Always at bottom */}
            <div className="pt-6 flex justify-between flex-shrink-0">
              <Button
                variant="outline"
                className="px-8"
                onClick={() => window.history.back()}
              >
                Back
              </Button>
              <Button
                className="px-8 text-white font-medium"
                style={{ backgroundColor: '#31860A' }}
                onClick={() => {
                  // Save goal data to context or localStorage
                  const goalData = {
                    selectedGoal,
                    targetWeight: isTargetWeightEnabled ? parseFloat(targetWeight) : null,
                    proteinPerKg,
                    calorieTarget,
                    macroDistribution: nutritionData.macros,
                    estimatedTimeToGoal: isTargetWeightEnabled ?
                      Math.abs(parseFloat(targetWeight) - userData.weight) / 0.5 : null // weeks
                  };

                  // Store in localStorage for persistence
                  localStorage.setItem('userGoalData', JSON.stringify(goalData));

                  // Show completion message
                  alert(`Goal "${selectedGoal.replace('-', ' ')}" saved successfully!\n` +
                    `Target: ${calorieTarget} calories/day\n` +
                    `Protein: ${nutritionData.macros[0].amount}g (${nutritionData.macros[0].percentage}%)`);
                }}
              >
                Complete
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
