'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UserData, CalculationResults, calculateAllResults, validateUserData } from '@/lib/calculations';

interface CalculatorContextType {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  calculations: CalculationResults | null;
  proteinPerKg: number;
  setProteinPerKg: (value: number) => void;
  validationErrors: string[];
  isDataValid: boolean;
  resetData: () => void;
  forceCalculation: () => void;
}

const defaultUserData: UserData = {
  gender: 'female',
  unitSystem: 'metric',
  age: 25,
  height: 170, // cm
  weight: 70, // kg
  activityLevel: 'moderate-activity',
  bodyFat: 15, // Default to 15%
  measurements: {
    waist: undefined,
    hips: undefined,
    neck: undefined
  }
};

const CalculatorContext = createContext<CalculatorContextType | undefined>(undefined);

export const useCalculator = () => {
  const context = useContext(CalculatorContext);
  if (context === undefined) {
    throw new Error('useCalculator must be used within a CalculatorProvider');
  }
  return context;
};

interface CalculatorProviderProps {
  children: ReactNode;
}

export const CalculatorProvider: React.FC<CalculatorProviderProps> = ({ children }) => {
  const [userData, setUserData] = useState<UserData>(defaultUserData);
  const [calculations, setCalculations] = useState<CalculationResults | null>(null);
  const [proteinPerKg, setProteinPerKg] = useState<number>(0.8); // Default protein intake
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isDataValid, setIsDataValid] = useState<boolean>(true);

  // Recalculate results whenever userData or proteinPerKg changes
  useEffect(() => {
    try {
      // Validate data first
      const validation = validateUserData(userData);
      setValidationErrors(validation.errors);
      setIsDataValid(validation.isValid);

      // Always calculate if basic required data is present, even if validation has minor issues
      if (userData.age && userData.height && userData.weight) {
        const results = calculateAllResults(userData, proteinPerKg);
        setCalculations(results);
      } else {
        setCalculations(null);
      }
    } catch (error) {
      console.error('Error calculating results:', error);
      setCalculations(null);
    }
  }, [userData, proteinPerKg]);

  // Also calculate immediately when component mounts with valid data
  useEffect(() => {
    if (userData.age && userData.height && userData.weight && !calculations) {
      try {
        const results = calculateAllResults(userData, proteinPerKg);
        setCalculations(results);
      } catch (error) {
        console.error('Error calculating initial results:', error);
      }
    }
  }, []);

  const updateUserData = (data: Partial<UserData>) => {
    setUserData(prevData => ({
      ...prevData,
      ...data,
      // Ensure measurements object exists
      measurements: {
        ...prevData.measurements,
        ...data.measurements
      }
    }));
  };

  const forceCalculation = () => {
    try {
      if (userData.age && userData.height && userData.weight) {
        const results = calculateAllResults(userData, proteinPerKg);
        setCalculations(results);
      }
    } catch (error) {
      console.error('Error forcing calculation:', error);
    }
  };

  const resetData = () => {
    setUserData(defaultUserData);
    setProteinPerKg(0.8);
    setCalculations(null);
    setValidationErrors([]);
    setIsDataValid(true);
  };

  const contextValue: CalculatorContextType = {
    userData,
    updateUserData,
    calculations,
    proteinPerKg,
    setProteinPerKg,
    validationErrors,
    isDataValid,
    resetData,
    forceCalculation
  };

  return (
    <CalculatorContext.Provider value={contextValue}>
      {children}
    </CalculatorContext.Provider>
  );
};

export default CalculatorContext;
