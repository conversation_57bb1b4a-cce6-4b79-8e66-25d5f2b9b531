// Fitness Calculator - Core Calculation Engine

export interface UserData {
  gender: "male" | "female";
  unitSystem: "metric" | "imperial";
  age: number;
  height: number; // Store in cm
  weight: number; // Store in kg
  activityLevel:
    | "sedentary"
    | "light-activity"
    | "moderate-activity"
    | "high-activity"
    | "very-high-activity";
  bodyFat?: number;
  measurements?: {
    waist?: number;
    hips?: number;
    neck?: number;
  };
}

export interface CalculationResults {
  bmr: number;
  bmi: {
    value: number;
    category: string;
    categoryIndex: number;
  };
  whr?: {
    value: number;
    category: string;
    description: string;
    imageIndex: number;
  };
  tdee: number;
  idealWeightRange: {
    lower: number;
    upper: number;
    target: number;
  };
  calorieTargets: {
    maintenance: number;
    rapidLoss: number;
    slowLoss: number;
    muscleGain: number;
  };
  weeklyFatLoss: {
    rapid: number;
    slow: number;
  };
  waterWeightFluctuation: number;
  proteinIntake: number;
}

// Activity level multipliers for TDEE calculation
export const activityMultipliers = {
  sedentary: 1.2,
  "light-activity": 1.375,
  "moderate-activity": 1.55,
  "high-activity": 1.725,
  "very-high-activity": 1.9,
} as const;

// BMI categories with colors for UI
export const bmiCategories = [
  { label: "Underweight", color: "#87CEEB", range: [0, 18.5] },
  { label: "Normal weight", color: "#90EE90", range: [18.5, 25] },
  { label: "Overweight", color: "#FFD700", range: [25, 30] },
  { label: "Obese I", color: "#FFA500", range: [30, 35] },
  { label: "Obese II", color: "#FF6347", range: [35, 40] },
  { label: "Obese III", color: "#DC143C", range: [40, 100] },
];

// Unit conversion functions
export const convertWeight = (
  weight: number,
  from: "kg" | "lbs",
  to: "kg" | "lbs"
): number => {
  if (from === to) return weight;
  if (from === "kg" && to === "lbs") return weight * 2.20462;
  if (from === "lbs" && to === "kg") return weight / 2.20462;
  return weight;
};

export const convertHeight = (
  height: number,
  from: "cm" | "ft-in",
  to: "cm" | "ft-in"
): number => {
  if (from === to) return height;
  if (from === "cm" && to === "ft-in") return height / 2.54; // Returns total inches
  if (from === "ft-in" && to === "cm") return height * 2.54; // Expects total inches
  return height;
};

// Core calculation functions
export const calculateBMR = (
  gender: "male" | "female",
  weight: number,
  height: number,
  age: number
): number => {
  // Mifflin-St Jeor Equation (more accurate than Harris-Benedict)
  // weight in kg, height in cm
  if (gender === "male") {
    return 88.362 + 13.397 * weight + 4.799 * height - 5.677 * age;
  } else {
    return 447.593 + 9.247 * weight + 3.098 * height - 4.33 * age;
  }
};

export const calculateBMI = (weight: number, height: number): number => {
  // weight in kg, height in cm
  const heightInMeters = height / 100;
  return weight / (heightInMeters * heightInMeters);
};

export const getBMICategory = (
  bmi: number
): { category: string; categoryIndex: number } => {
  for (let i = 0; i < bmiCategories.length; i++) {
    const [min, max] = bmiCategories[i].range;
    if (bmi >= min && bmi < max) {
      return { category: bmiCategories[i].label, categoryIndex: i };
    }
  }
  return {
    category: bmiCategories[bmiCategories.length - 1].label,
    categoryIndex: bmiCategories.length - 1,
  };
};

export const calculateWHR = (waist: number, hips: number): number => {
  return waist / hips;
};

export const getWHRCategory = (
  whr: number,
  gender: "male" | "female"
): { category: string; description: string; imageIndex: number } => {
  // WHR Categories with 4 ranges:
  // 0.40-0.85: Peripheral Body Type (whr-0.svg)
  // 0.86-0.90: Balanced Character Type (whr-1.svg)
  // 0.91-0.94: Central Character Type (whr-2.svg)
  // 0.95-1.00+: Risky Character Type (whr-3.svg)

  if (whr <= 0.85) {
    return {
      category: "Peripheral Body Type",
      description:
        "Fat accumulates in your body especially on your hips and buttocks. It does not pose a major health risk. This body type is primarily determined by genetics.",
      imageIndex: 0,
    };
  } else if (whr <= 0.9) {
    return {
      category: "Balanced Character Type",
      description:
        "Fat in your body accumulates evenly. From a health point of view (in relation to the distribution of fat on the body), this body type is considered ideal and there is no greater risk of health complications.",
      imageIndex: 1,
    };
  } else if (whr <= 0.94) {
    return {
      category: "Central Character Type",
      description:
        "Fat accumulates in your abdomen to a greater extent. Even if the waist circumference is smaller than the hip circumference, there is an increased risk of health conditions.",
      imageIndex: 2,
    };
  } else {
    return {
      category: "Risky Character Type",
      description:
        "You have excess fat reserves in the abdominal area. Apple-type obesity is a risk factor for cardiovascular disease, stroke, high blood pressure or type 2 diabetes.",
      imageIndex: 3,
    };
  }
};

export const calculateTDEE = (
  bmr: number,
  activityLevel: keyof typeof activityMultipliers
): number => {
  return bmr * activityMultipliers[activityLevel];
};

export const calculateIdealWeightRange = (height: number) => {
  // height in cm
  const heightInMeters = height / 100;
  const lower = 18.5 * (heightInMeters * heightInMeters);
  const upper = 24.9 * (heightInMeters * heightInMeters);
  const target = (lower + upper) / 2;

  return { lower, upper, target };
};

export const calculateCalorieTargets = (tdee: number) => {
  return {
    maintenance: tdee,
    rapidLoss: tdee - 700,
    slowLoss: tdee - 400,
    muscleGain: tdee + 300,
  };
};

export const calculateWeeklyFatLoss = (calorieDeficit: number): number => {
  // 1 pound of fat = approximately 3500 calories
  return (calorieDeficit * 7) / 3500;
};

export const calculateWaterWeightFluctuation = (weight: number): number => {
  // Estimate 1-2% of body weight as daily fluctuation
  return weight * 0.015; // 1.5% average
};

export const calculateProteinIntake = (
  targetWeight: number,
  proteinPerKg: number = 0.8
): number => {
  return targetWeight * proteinPerKg;
};

// Main calculation function that processes all user data
export const calculateAllResults = (
  userData: UserData,
  proteinPerKg: number = 0.8
): CalculationResults => {
  const bmr = calculateBMR(
    userData.gender,
    userData.weight,
    userData.height,
    userData.age
  );
  const bmiValue = calculateBMI(userData.weight, userData.height);
  const bmiData = getBMICategory(bmiValue);
  const tdee = calculateTDEE(bmr, userData.activityLevel);
  const idealWeightRange = calculateIdealWeightRange(userData.height);
  const calorieTargets = calculateCalorieTargets(tdee);

  let whr = undefined;
  if (userData.measurements?.waist && userData.measurements?.hips) {
    const whrValue = calculateWHR(
      userData.measurements.waist,
      userData.measurements.hips
    );
    const whrCategory = getWHRCategory(whrValue, userData.gender);
    whr = {
      value: whrValue,
      category: whrCategory.category,
      description: whrCategory.description,
      imageIndex: whrCategory.imageIndex,
    };
  }

  return {
    bmr,
    bmi: {
      value: bmiValue,
      category: bmiData.category,
      categoryIndex: bmiData.categoryIndex,
    },
    whr,
    tdee,
    idealWeightRange,
    calorieTargets,
    weeklyFatLoss: {
      rapid: calculateWeeklyFatLoss(700),
      slow: calculateWeeklyFatLoss(400),
    },
    waterWeightFluctuation: calculateWaterWeightFluctuation(userData.weight),
    proteinIntake: calculateProteinIntake(
      idealWeightRange.target,
      proteinPerKg
    ),
  };
};

// Validation functions
export const validateUserData = (
  userData: Partial<UserData>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (userData.age !== undefined && (userData.age < 18 || userData.age > 80)) {
    errors.push("Age must be between 18 and 80 years");
  }

  if (
    userData.weight !== undefined &&
    (userData.weight < 30 || userData.weight > 300)
  ) {
    errors.push("Weight must be between 30 and 300 kg");
  }

  if (
    userData.height !== undefined &&
    (userData.height < 100 || userData.height > 250)
  ) {
    errors.push("Height must be between 100 and 250 cm");
  }

  if (
    userData.bodyFat !== undefined &&
    (userData.bodyFat < 3 || userData.bodyFat > 50)
  ) {
    errors.push("Body fat percentage must be between 3% and 50%");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
